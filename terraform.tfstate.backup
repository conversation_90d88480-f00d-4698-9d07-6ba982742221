{"version": 4, "terraform_version": "1.12.2", "serial": 116, "lineage": "350a2367-4e9b-3975-b672-7d6dc91670d9", "outputs": {}, "resources": [], "check_results": [{"object_kind": "var", "config_addr": "module.management.module.alz.module.identity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.custom_landing_zones", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.create_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_group_archetypes.var.scope_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_enforcement_placeholder", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.destroy_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.root_name", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.custom_landing_zones", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_connectivity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_management", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_default", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_identity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.destroy_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.var.subscription_id_management", "status": "unknown", "objects": [{"object_addr": "module.management.var.subscription_id_management", "status": "unknown"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_group_archetypes.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforcement_placeholder", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_default", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_not_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_connectivity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.log_analytics.var.sku", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.root_name", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_management", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.root_parent_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.create_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.identity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.root_parent_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_not_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_group_archetypes.var.scope_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_group_archetypes.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.log_analytics.var.retention_in_days", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_identity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.root_id", "status": "unknown", "objects": null}]}