{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "connectivity", "Source": "./connectivity", "Dir": "connectivity"}, {"Key": "connectivity.alz_connectivity", "Source": "registry.terraform.io/Azure/caf-enterprise-scale/azurerm", "Version": "6.2.1", "Dir": ".terraform/modules/connectivity.alz_connectivity"}, {"Key": "connectivity.alz_connectivity.connectivity_resources", "Source": "./modules/connectivity", "Dir": ".terraform/modules/connectivity.alz_connectivity/modules/connectivity"}, {"Key": "connectivity.alz_connectivity.identity_resources", "Source": "./modules/identity", "Dir": ".terraform/modules/connectivity.alz_connectivity/modules/identity"}, {"Key": "connectivity.alz_connectivity.management_group_archetypes", "Source": "./modules/archetypes", "Dir": ".terraform/modules/connectivity.alz_connectivity/modules/archetypes"}, {"Key": "connectivity.alz_connectivity.management_resources", "Source": "./modules/management", "Dir": ".terraform/modules/connectivity.alz_connectivity/modules/management"}, {"Key": "connectivity.alz_connectivity.role_assignments_for_policy", "Source": "./modules/role_assignments_for_policy", "Dir": ".terraform/modules/connectivity.alz_connectivity/modules/role_assignments_for_policy"}, {"Key": "management", "Source": "./management", "Dir": "management"}, {"Key": "management.alz", "Source": "registry.terraform.io/Azure/caf-enterprise-scale/azurerm", "Version": "6.2.0", "Dir": ".terraform/modules/management.alz"}, {"Key": "management.alz.connectivity_resources", "Source": "./modules/connectivity", "Dir": ".terraform/modules/management.alz/modules/connectivity"}, {"Key": "management.alz.identity_resources", "Source": "./modules/identity", "Dir": ".terraform/modules/management.alz/modules/identity"}, {"Key": "management.alz.management_group_archetypes", "Source": "./modules/archetypes", "Dir": ".terraform/modules/management.alz/modules/archetypes"}, {"Key": "management.alz.management_resources", "Source": "./modules/management", "Dir": ".terraform/modules/management.alz/modules/management"}, {"Key": "management.alz.role_assignments_for_policy", "Source": "./modules/role_assignments_for_policy", "Dir": ".terraform/modules/management.alz/modules/role_assignments_for_policy"}, {"Key": "management.azure_monitor_agent", "Source": "./modules/azure_monitor_agent", "Dir": "management/modules/azure_monitor_agent"}, {"Key": "management.log_analytics", "Source": "./modules/log_analytics", "Dir": "management/modules/log_analytics"}, {"Key": "management.security_center", "Source": "./modules/security_center", "Dir": "management/modules/security_center"}, {"Key": "spokes", "Source": "./spoke", "Dir": "spoke"}]}